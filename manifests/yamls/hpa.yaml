# Legacy HPA api in case autoscaling/v2beta2 is unavailable
apiVersion: autoscaling/v2beta1
kind: HorizontalPodAutoscaler
metadata:
 name: lens-hpa
spec:
 scaleTargetRef:
   apiVersion: apps/v1
   kind: Deployment
   name: lens
 minReplicas: 1
 maxReplicas: 2
 metrics:
   - type: Resource
     resource:
       name: memory
       targetAverageUtilization: 90
   - type: Resource
     resource:
       name: cpu
       targetAverageUtilization: 80
---
# Source: lens/templates/hpa.yaml
apiVersion: autoscaling/v2beta2
kind: HorizontalPodAutoscaler
metadata:
 name: lens-hpa
spec:
 scaleTargetRef:
   apiVersion: apps/v1
   kind: Deployment
   name: lens
 minReplicas: 1
 maxReplicas: 2
 metrics:
   - type: Resource
     resource:
        name: memory
        target:
          type: Utilization
          averageUtilization: 90
   - type: Resource
     resource:
        name: cpu
        target:
          type: Utilization
          averageUtilization: 80
---
# Legacy HPA api in case autoscaling/v2beta2 is unavailable
apiVersion: autoscaling/v2beta1
kind: HorizontalPodAutoscaler
metadata:
 name: guard-hpa
spec:
 scaleTargetRef:
   apiVersion: apps/v1
   kind: Deployment
   name: guard
 minReplicas: 1
 maxReplicas: 4
 metrics:
   - type: Resource
     resource:
       name: memory
       targetAverageUtilization: 80
   - type: Resource
     resource:
       name: cpu
       targetAverageUtilization: 90
---
# Source: guard/templates/hpa.yaml
apiVersion: autoscaling/v2beta2
kind: HorizontalPodAutoscaler
metadata:
  name: guard-hpa
  labels:
    release: devtron
spec:
  scaleTargetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: guard
  minReplicas: 1
  maxReplicas: 4
  metrics:
    - type: Resource
      resource:
        name: memory
        target:
          type: Utilization
          averageUtilization: 80
    - type: Resource
      resource:
        name: cpu
        target:
          type: Utilization
          averageUtilization:  90
---
