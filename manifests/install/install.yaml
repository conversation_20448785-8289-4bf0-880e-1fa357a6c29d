apiVersion: v1
kind: ServiceAccount
metadata:
  name: installer
  namespace: devtroncd
---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRoleBinding
metadata:
  name: installer
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: ClusterRole
  name: cluster-admin
subjects:
  - kind: ServiceAccount
    name: installer
    namespace: devtroncd
---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRoleBinding
metadata:
  name: installer-editor
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: ClusterRole
  name: installer-editor-role
subjects:
  - kind: ServiceAccount
    name: installer
    namespace: devtroncd
---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  name: installer-editor-role
rules:
  - apiGroups:
      - installer.devtron.ai
    resources:
      - installers
    verbs:
      - create
      - delete
      - get
      - list
      - patch
      - update
      - watch
  - apiGroups:
      - installer.devtron.ai
    resources:
      - installers/status
    verbs:
      - get
---
apiVersion: apps/v1
kind: Deployment
metadata:
  labels:
    app: inception
  name: inception
  namespace: devtroncd
spec:
  minReadySeconds: 60
  replicas: 1
  strategy:
    type:
      Recreate
  selector:
    matchLabels:
      app: inception
  template:
    metadata:
      creationTimestamp: null
      labels:
        app: inception
    spec:
      containers:
        -
          image: quay.io/devtron/inception:473deaa4-185-21582
          imagePullPolicy: IfNotPresent
          name: inception
          ports:
            -
              containerPort: 8080
              name: app
              protocol: TCP
      restartPolicy: Always
      serviceAccountName: installer
      terminationGracePeriodSeconds: 30
