// Code generated by mockery v2.32.0. DO NOT EDIT.

package mocks

import (
	context "context"
	"github.com/devtron-labs/devtron/api/helm-app/bean"
	client "github.com/devtron-labs/devtron/api/helm-app/gRPC"
	helmBean "github.com/devtron-labs/devtron/api/helm-app/service/bean"

	http "net/http"

	mock "github.com/stretchr/testify/mock"

	openapi "github.com/devtron-labs/devtron/api/helm-app/openapiClient"

	openapiClient "github.com/devtron-labs/devtron/api/openapi/openapiClient"
)

// HelmAppService is an autogenerated mock type for the HelmAppService type
type HelmAppService struct {
	mock.Mock
}

// DecodeAppId provides a mock function with given fields: appId
func (_m *HelmAppService) DecodeAppId(appId string) (*helmBean.AppIdentifier, error) {
	ret := _m.Called(appId)

	var r0 *helmBean.AppIdentifier
	var r1 error
	if rf, ok := ret.Get(0).(func(string) (*helmBean.AppIdentifier, error)); ok {
		return rf(appId)
	}
	if rf, ok := ret.Get(0).(func(string) *helmBean.AppIdentifier); ok {
		r0 = rf(appId)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*helmBean.AppIdentifier)
		}
	}

	if rf, ok := ret.Get(1).(func(string) error); ok {
		r1 = rf(appId)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// DeleteApplication provides a mock function with given fields: ctx, app
func (_m *HelmAppService) DeleteApplication(ctx context.Context, app *helmBean.AppIdentifier) (*openapi.UninstallReleaseResponse, error) {
	ret := _m.Called(ctx, app)

	var r0 *openapi.UninstallReleaseResponse
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, *helmBean.AppIdentifier) (*openapi.UninstallReleaseResponse, error)); ok {
		return rf(ctx, app)
	}
	if rf, ok := ret.Get(0).(func(context.Context, *helmBean.AppIdentifier) *openapi.UninstallReleaseResponse); ok {
		r0 = rf(ctx, app)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*openapi.UninstallReleaseResponse)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, *helmBean.AppIdentifier) error); ok {
		r1 = rf(ctx, app)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// EncodeAppId provides a mock function with given fields: appIdentifier
func (_m *HelmAppService) EncodeAppId(appIdentifier *helmBean.AppIdentifier) string {
	ret := _m.Called(appIdentifier)

	var r0 string
	if rf, ok := ret.Get(0).(func(*helmBean.AppIdentifier) string); ok {
		r0 = rf(appIdentifier)
	} else {
		r0 = ret.Get(0).(string)
	}

	return r0
}

// GetApplicationDetail provides a mock function with given fields: ctx, app
func (_m *HelmAppService) GetApplicationDetail(ctx context.Context, app *helmBean.AppIdentifier) (*client.AppDetail, error) {
	ret := _m.Called(ctx, app)

	var r0 *client.AppDetail
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, *helmBean.AppIdentifier) (*client.AppDetail, error)); ok {
		return rf(ctx, app)
	}
	if rf, ok := ret.Get(0).(func(context.Context, *helmBean.AppIdentifier) *client.AppDetail); ok {
		r0 = rf(ctx, app)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*client.AppDetail)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, *helmBean.AppIdentifier) error); ok {
		r1 = rf(ctx, app)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// GetApplicationDetailWithFilter provides a mock function with given fields: ctx, app, resourceTreeFilter
func (_m *HelmAppService) GetApplicationDetailWithFilter(ctx context.Context, app *helmBean.AppIdentifier, resourceTreeFilter *client.ResourceTreeFilter) (*client.AppDetail, error) {
	ret := _m.Called(ctx, app, resourceTreeFilter)

	var r0 *client.AppDetail
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, *helmBean.AppIdentifier, *client.ResourceTreeFilter) (*client.AppDetail, error)); ok {
		return rf(ctx, app, resourceTreeFilter)
	}
	if rf, ok := ret.Get(0).(func(context.Context, *helmBean.AppIdentifier, *client.ResourceTreeFilter) *client.AppDetail); ok {
		r0 = rf(ctx, app, resourceTreeFilter)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*client.AppDetail)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, *helmBean.AppIdentifier, *client.ResourceTreeFilter) error); ok {
		r1 = rf(ctx, app, resourceTreeFilter)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// GetApplicationStatus provides a mock function with given fields: ctx, app
func (_m *HelmAppService) GetApplicationStatus(ctx context.Context, app *helmBean.AppIdentifier) (string, error) {
	ret := _m.Called(ctx, app)

	var r0 string
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, *helmBean.AppIdentifier) (string, error)); ok {
		return rf(ctx, app)
	}
	if rf, ok := ret.Get(0).(func(context.Context, *helmBean.AppIdentifier) string); ok {
		r0 = rf(ctx, app)
	} else {
		r0 = ret.Get(0).(string)
	}

	if rf, ok := ret.Get(1).(func(context.Context, *helmBean.AppIdentifier) error); ok {
		r1 = rf(ctx, app)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// GetClusterConf provides a mock function with given fields: clusterId
func (_m *HelmAppService) GetClusterConf(clusterId int) (*client.ClusterConfig, error) {
	ret := _m.Called(clusterId)

	var r0 *client.ClusterConfig
	var r1 error
	if rf, ok := ret.Get(0).(func(int) (*client.ClusterConfig, error)); ok {
		return rf(clusterId)
	}
	if rf, ok := ret.Get(0).(func(int) *client.ClusterConfig); ok {
		r0 = rf(clusterId)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*client.ClusterConfig)
		}
	}

	if rf, ok := ret.Get(1).(func(int) error); ok {
		r1 = rf(clusterId)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// GetDeploymentDetail provides a mock function with given fields: ctx, app, version
func (_m *HelmAppService) GetDeploymentDetail(ctx context.Context, app *helmBean.AppIdentifier, version int32) (*openapi.HelmAppDeploymentManifestDetail, error) {
	ret := _m.Called(ctx, app, version)

	var r0 *openapi.HelmAppDeploymentManifestDetail
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, *helmBean.AppIdentifier, int32) (*openapi.HelmAppDeploymentManifestDetail, error)); ok {
		return rf(ctx, app, version)
	}
	if rf, ok := ret.Get(0).(func(context.Context, *helmBean.AppIdentifier, int32) *openapi.HelmAppDeploymentManifestDetail); ok {
		r0 = rf(ctx, app, version)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*openapi.HelmAppDeploymentManifestDetail)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, *helmBean.AppIdentifier, int32) error); ok {
		r1 = rf(ctx, app, version)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// GetDeploymentHistory provides a mock function with given fields: ctx, app
func (_m *HelmAppService) GetDeploymentHistory(ctx context.Context, app *helmBean.AppIdentifier) (*client.HelmAppDeploymentHistory, error) {
	ret := _m.Called(ctx, app)

	var r0 *client.HelmAppDeploymentHistory
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, *helmBean.AppIdentifier) (*client.HelmAppDeploymentHistory, error)); ok {
		return rf(ctx, app)
	}
	if rf, ok := ret.Get(0).(func(context.Context, *helmBean.AppIdentifier) *client.HelmAppDeploymentHistory); ok {
		r0 = rf(ctx, app)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*client.HelmAppDeploymentHistory)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, *helmBean.AppIdentifier) error); ok {
		r1 = rf(ctx, app)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// GetDesiredManifest provides a mock function with given fields: ctx, app, resource
func (_m *HelmAppService) GetDesiredManifest(ctx context.Context, app *helmBean.AppIdentifier, resource *openapi.ResourceIdentifier) (*openapi.DesiredManifestResponse, error) {
	ret := _m.Called(ctx, app, resource)

	var r0 *openapi.DesiredManifestResponse
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, *helmBean.AppIdentifier, *openapi.ResourceIdentifier) (*openapi.DesiredManifestResponse, error)); ok {
		return rf(ctx, app, resource)
	}
	if rf, ok := ret.Get(0).(func(context.Context, *helmBean.AppIdentifier, *openapi.ResourceIdentifier) *openapi.DesiredManifestResponse); ok {
		r0 = rf(ctx, app, resource)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*openapi.DesiredManifestResponse)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, *helmBean.AppIdentifier, *openapi.ResourceIdentifier) error); ok {
		r1 = rf(ctx, app, resource)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// GetDevtronHelmAppIdentifier provides a mock function with given fields:
func (_m *HelmAppService) GetDevtronHelmAppIdentifier() *helmBean.AppIdentifier {
	ret := _m.Called()

	var r0 *helmBean.AppIdentifier
	if rf, ok := ret.Get(0).(func() *helmBean.AppIdentifier); ok {
		r0 = rf()
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*helmBean.AppIdentifier)
		}
	}

	return r0
}

// GetNotes provides a mock function with given fields: ctx, request
func (_m *HelmAppService) GetNotes(ctx context.Context, request *client.InstallReleaseRequest) (string, error) {
	ret := _m.Called(ctx, request)

	var r0 string
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, *client.InstallReleaseRequest) (string, error)); ok {
		return rf(ctx, request)
	}
	if rf, ok := ret.Get(0).(func(context.Context, *client.InstallReleaseRequest) string); ok {
		r0 = rf(ctx, request)
	} else {
		r0 = ret.Get(0).(string)
	}

	if rf, ok := ret.Get(1).(func(context.Context, *client.InstallReleaseRequest) error); ok {
		r1 = rf(ctx, request)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// GetRevisionHistoryMaxValue provides a mock function with given fields: appType
func (_m *HelmAppService) GetRevisionHistoryMaxValue(appType bean.SourceAppType) int32 {
	ret := _m.Called(appType)

	var r0 int32
	if rf, ok := ret.Get(0).(func(bean.SourceAppType) int32); ok {
		r0 = rf(appType)
	} else {
		r0 = ret.Get(0).(int32)
	}

	return r0
}

// GetValuesYaml provides a mock function with given fields: ctx, app
func (_m *HelmAppService) GetValuesYaml(ctx context.Context, app *helmBean.AppIdentifier) (*client.ReleaseInfo, error) {
	ret := _m.Called(ctx, app)

	var r0 *client.ReleaseInfo
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, *helmBean.AppIdentifier) (*client.ReleaseInfo, error)); ok {
		return rf(ctx, app)
	}
	if rf, ok := ret.Get(0).(func(context.Context, *helmBean.AppIdentifier) *client.ReleaseInfo); ok {
		r0 = rf(ctx, app)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*client.ReleaseInfo)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, *helmBean.AppIdentifier) error); ok {
		r1 = rf(ctx, app)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// HibernateApplication provides a mock function with given fields: ctx, app, hibernateRequest
func (_m *HelmAppService) HibernateApplication(ctx context.Context, app *helmBean.AppIdentifier, hibernateRequest *openapi.HibernateRequest) ([]*openapi.HibernateStatus, error) {
	ret := _m.Called(ctx, app, hibernateRequest)

	var r0 []*openapi.HibernateStatus
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, *helmBean.AppIdentifier, *openapi.HibernateRequest) ([]*openapi.HibernateStatus, error)); ok {
		return rf(ctx, app, hibernateRequest)
	}
	if rf, ok := ret.Get(0).(func(context.Context, *helmBean.AppIdentifier, *openapi.HibernateRequest) []*openapi.HibernateStatus); ok {
		r0 = rf(ctx, app, hibernateRequest)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]*openapi.HibernateStatus)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, *helmBean.AppIdentifier, *openapi.HibernateRequest) error); ok {
		r1 = rf(ctx, app, hibernateRequest)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// InstallRelease provides a mock function with given fields: ctx, clusterId, installReleaseRequest
func (_m *HelmAppService) InstallRelease(ctx context.Context, clusterId int, installReleaseRequest *client.InstallReleaseRequest) (*client.InstallReleaseResponse, error) {
	ret := _m.Called(ctx, clusterId, installReleaseRequest)

	var r0 *client.InstallReleaseResponse
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, int, *client.InstallReleaseRequest) (*client.InstallReleaseResponse, error)); ok {
		return rf(ctx, clusterId, installReleaseRequest)
	}
	if rf, ok := ret.Get(0).(func(context.Context, int, *client.InstallReleaseRequest) *client.InstallReleaseResponse); ok {
		r0 = rf(ctx, clusterId, installReleaseRequest)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*client.InstallReleaseResponse)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, int, *client.InstallReleaseRequest) error); ok {
		r1 = rf(ctx, clusterId, installReleaseRequest)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// IsReleaseInstalled provides a mock function with given fields: ctx, app
func (_m *HelmAppService) IsReleaseInstalled(ctx context.Context, app *helmBean.AppIdentifier) (bool, error) {
	ret := _m.Called(ctx, app)

	var r0 bool
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, *helmBean.AppIdentifier) (bool, error)); ok {
		return rf(ctx, app)
	}
	if rf, ok := ret.Get(0).(func(context.Context, *helmBean.AppIdentifier) bool); ok {
		r0 = rf(ctx, app)
	} else {
		r0 = ret.Get(0).(bool)
	}

	if rf, ok := ret.Get(1).(func(context.Context, *helmBean.AppIdentifier) error); ok {
		r1 = rf(ctx, app)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// ListHelmApplications provides a mock function with given fields: ctx, clusterIds, w, token, helmAuth
func (_m *HelmAppService) ListHelmApplications(ctx context.Context, clusterIds []int, w http.ResponseWriter, token string, helmAuth func(string, string) bool) {
	_m.Called(ctx, clusterIds, w, token, helmAuth)
}

// RollbackRelease provides a mock function with given fields: ctx, app, version
func (_m *HelmAppService) RollbackRelease(ctx context.Context, app *helmBean.AppIdentifier, version int32) (bool, error) {
	ret := _m.Called(ctx, app, version)

	var r0 bool
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, *helmBean.AppIdentifier, int32) (bool, error)); ok {
		return rf(ctx, app, version)
	}
	if rf, ok := ret.Get(0).(func(context.Context, *helmBean.AppIdentifier, int32) bool); ok {
		r0 = rf(ctx, app, version)
	} else {
		r0 = ret.Get(0).(bool)
	}

	if rf, ok := ret.Get(1).(func(context.Context, *helmBean.AppIdentifier, int32) error); ok {
		r1 = rf(ctx, app, version)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// TemplateChart provides a mock function with given fields: ctx, templateChartRequest
func (_m *HelmAppService) TemplateChart(ctx context.Context, templateChartRequest *openapiClient.TemplateChartRequest) (*openapiClient.TemplateChartResponse, error) {
	ret := _m.Called(ctx, templateChartRequest)

	var r0 *openapiClient.TemplateChartResponse
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, *openapiClient.TemplateChartRequest) (*openapiClient.TemplateChartResponse, error)); ok {
		return rf(ctx, templateChartRequest)
	}
	if rf, ok := ret.Get(0).(func(context.Context, *openapiClient.TemplateChartRequest) *openapiClient.TemplateChartResponse); ok {
		r0 = rf(ctx, templateChartRequest)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*openapiClient.TemplateChartResponse)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, *openapiClient.TemplateChartRequest) error); ok {
		r1 = rf(ctx, templateChartRequest)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// UnHibernateApplication provides a mock function with given fields: ctx, app, hibernateRequest
func (_m *HelmAppService) UnHibernateApplication(ctx context.Context, app *helmBean.AppIdentifier, hibernateRequest *openapi.HibernateRequest) ([]*openapi.HibernateStatus, error) {
	ret := _m.Called(ctx, app, hibernateRequest)

	var r0 []*openapi.HibernateStatus
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, *helmBean.AppIdentifier, *openapi.HibernateRequest) ([]*openapi.HibernateStatus, error)); ok {
		return rf(ctx, app, hibernateRequest)
	}
	if rf, ok := ret.Get(0).(func(context.Context, *helmBean.AppIdentifier, *openapi.HibernateRequest) []*openapi.HibernateStatus); ok {
		r0 = rf(ctx, app, hibernateRequest)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]*openapi.HibernateStatus)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, *helmBean.AppIdentifier, *openapi.HibernateRequest) error); ok {
		r1 = rf(ctx, app, hibernateRequest)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// UpdateApplication provides a mock function with given fields: ctx, app, request
func (_m *HelmAppService) UpdateApplication(ctx context.Context, app *helmBean.AppIdentifier, request *bean.UpdateApplicationRequestDto) (*openapi.UpdateReleaseResponse, error) {
	ret := _m.Called(ctx, app, request)

	var r0 *openapi.UpdateReleaseResponse
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, *helmBean.AppIdentifier, *bean.UpdateApplicationRequestDto) (*openapi.UpdateReleaseResponse, error)); ok {
		return rf(ctx, app, request)
	}
	if rf, ok := ret.Get(0).(func(context.Context, *helmBean.AppIdentifier, *bean.UpdateApplicationRequestDto) *openapi.UpdateReleaseResponse); ok {
		r0 = rf(ctx, app, request)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*openapi.UpdateReleaseResponse)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, *helmBean.AppIdentifier, *bean.UpdateApplicationRequestDto) error); ok {
		r1 = rf(ctx, app, request)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// UpdateApplicationWithChartInfo provides a mock function with given fields: ctx, clusterId, request
func (_m *HelmAppService) UpdateApplicationWithChartInfo(ctx context.Context, clusterId int, request *bean.UpdateApplicationWithChartInfoRequestDto) (*openapi.UpdateReleaseResponse, error) {
	ret := _m.Called(ctx, clusterId, request)

	var r0 *openapi.UpdateReleaseResponse
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, int, *bean.UpdateApplicationWithChartInfoRequestDto) (*openapi.UpdateReleaseResponse, error)); ok {
		return rf(ctx, clusterId, request)
	}
	if rf, ok := ret.Get(0).(func(context.Context, int, *bean.UpdateApplicationWithChartInfoRequestDto) *openapi.UpdateReleaseResponse); ok {
		r0 = rf(ctx, clusterId, request)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*openapi.UpdateReleaseResponse)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, int, *bean.UpdateApplicationWithChartInfoRequestDto) error); ok {
		r1 = rf(ctx, clusterId, request)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// UpdateApplicationWithChartInfoWithExtraValues provides a mock function with given fields: ctx, appIdentifier, chartRepository, extraValues, extraValuesYamlUrl, useLatestChartVersion
func (_m *HelmAppService) UpdateApplicationWithChartInfoWithExtraValues(ctx context.Context, appIdentifier *helmBean.AppIdentifier, chartRepository *client.ChartRepository, extraValues map[string]interface{}, extraValuesYamlUrl string, useLatestChartVersion bool) (*openapi.UpdateReleaseResponse, error) {
	ret := _m.Called(ctx, appIdentifier, chartRepository, extraValues, extraValuesYamlUrl, useLatestChartVersion)

	var r0 *openapi.UpdateReleaseResponse
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, *helmBean.AppIdentifier, *client.ChartRepository, map[string]interface{}, string, bool) (*openapi.UpdateReleaseResponse, error)); ok {
		return rf(ctx, appIdentifier, chartRepository, extraValues, extraValuesYamlUrl, useLatestChartVersion)
	}
	if rf, ok := ret.Get(0).(func(context.Context, *helmBean.AppIdentifier, *client.ChartRepository, map[string]interface{}, string, bool) *openapi.UpdateReleaseResponse); ok {
		r0 = rf(ctx, appIdentifier, chartRepository, extraValues, extraValuesYamlUrl, useLatestChartVersion)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*openapi.UpdateReleaseResponse)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, *helmBean.AppIdentifier, *client.ChartRepository, map[string]interface{}, string, bool) error); ok {
		r1 = rf(ctx, appIdentifier, chartRepository, extraValues, extraValuesYamlUrl, useLatestChartVersion)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// ValidateOCIRegistry provides a mock function with given fields: ctx, OCIRegistryRequest
func (_m *HelmAppService) ValidateOCIRegistry(ctx context.Context, OCIRegistryRequest *client.RegistryCredential) bool {
	ret := _m.Called(ctx, OCIRegistryRequest)

	var r0 bool
	if rf, ok := ret.Get(0).(func(context.Context, *client.RegistryCredential) bool); ok {
		r0 = rf(ctx, OCIRegistryRequest)
	} else {
		r0 = ret.Get(0).(bool)
	}

	return r0
}

// NewHelmAppService creates a new instance of HelmAppService. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewHelmAppService(t interface {
	mock.TestingT
	Cleanup(func())
}) *HelmAppService {
	mock := &HelmAppService{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}
