/*
Devtron Labs

No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)

API version: 1.0.0
*/

// Code generated by OpenAPI Generator (https://openapi-generator.tech); DO NOT EDIT.

package openapi

import (
	"encoding/json"
)

// HibernateStatus struct for HibernateStatus
type HibernateStatus struct {
	// operation was success or not
	Success *bool `json:"success,omitempty"`
	// failure cause, empty is success
	ErrorMessage *string                `json:"errorMessage,omitempty"`
	TargetObject *HibernateTargetObject `json:"targetObject,omitempty"`
}

// NewHibernateStatus instantiates a new HibernateStatus object
// This constructor will assign default values to properties that have it defined,
// and makes sure properties required by API are set, but the set of arguments
// will change when the set of required properties is changed
func NewHibernateStatus() *HibernateStatus {
	this := HibernateStatus{}
	return &this
}

// NewHibernateStatusWithDefaults instantiates a new HibernateStatus object
// This constructor will only assign default values to properties that have it defined,
// but it doesn't guarantee that properties required by API are set
func NewHibernateStatusWithDefaults() *HibernateStatus {
	this := HibernateStatus{}
	return &this
}

// GetSuccess returns the Success field value if set, zero value otherwise.
func (o *HibernateStatus) GetSuccess() bool {
	if o == nil || o.Success == nil {
		var ret bool
		return ret
	}
	return *o.Success
}

// GetSuccessOk returns a tuple with the Success field value if set, nil otherwise
// and a boolean to check if the value has been set.
func (o *HibernateStatus) GetSuccessOk() (*bool, bool) {
	if o == nil || o.Success == nil {
		return nil, false
	}
	return o.Success, true
}

// HasSuccess returns a boolean if a field has been set.
func (o *HibernateStatus) HasSuccess() bool {
	if o != nil && o.Success != nil {
		return true
	}

	return false
}

// SetSuccess gets a reference to the given bool and assigns it to the Success field.
func (o *HibernateStatus) SetSuccess(v bool) {
	o.Success = &v
}

// GetErrorMessage returns the ErrorMessage field value if set, zero value otherwise.
func (o *HibernateStatus) GetErrorMessage() string {
	if o == nil || o.ErrorMessage == nil {
		var ret string
		return ret
	}
	return *o.ErrorMessage
}

// GetErrorMessageOk returns a tuple with the ErrorMessage field value if set, nil otherwise
// and a boolean to check if the value has been set.
func (o *HibernateStatus) GetErrorMessageOk() (*string, bool) {
	if o == nil || o.ErrorMessage == nil {
		return nil, false
	}
	return o.ErrorMessage, true
}

// HasErrorMessage returns a boolean if a field has been set.
func (o *HibernateStatus) HasErrorMessage() bool {
	if o != nil && o.ErrorMessage != nil {
		return true
	}

	return false
}

// SetErrorMessage gets a reference to the given string and assigns it to the ErrorMessage field.
func (o *HibernateStatus) SetErrorMessage(v string) {
	o.ErrorMessage = &v
}

// GetTargetObject returns the TargetObject field value if set, zero value otherwise.
func (o *HibernateStatus) GetTargetObject() HibernateTargetObject {
	if o == nil || o.TargetObject == nil {
		var ret HibernateTargetObject
		return ret
	}
	return *o.TargetObject
}

// GetTargetObjectOk returns a tuple with the TargetObject field value if set, nil otherwise
// and a boolean to check if the value has been set.
func (o *HibernateStatus) GetTargetObjectOk() (*HibernateTargetObject, bool) {
	if o == nil || o.TargetObject == nil {
		return nil, false
	}
	return o.TargetObject, true
}

// HasTargetObject returns a boolean if a field has been set.
func (o *HibernateStatus) HasTargetObject() bool {
	if o != nil && o.TargetObject != nil {
		return true
	}

	return false
}

// SetTargetObject gets a reference to the given HibernateTargetObject and assigns it to the TargetObject field.
func (o *HibernateStatus) SetTargetObject(v HibernateTargetObject) {
	o.TargetObject = &v
}

func (o HibernateStatus) MarshalJSON() ([]byte, error) {
	toSerialize := map[string]interface{}{}
	if o.Success != nil {
		toSerialize["success"] = o.Success
	}
	if o.ErrorMessage != nil {
		toSerialize["errorMessage"] = o.ErrorMessage
	}
	if o.TargetObject != nil {
		toSerialize["targetObject"] = o.TargetObject
	}
	return json.Marshal(toSerialize)
}

type NullableHibernateStatus struct {
	value *HibernateStatus
	isSet bool
}

func (v NullableHibernateStatus) Get() *HibernateStatus {
	return v.value
}

func (v *NullableHibernateStatus) Set(val *HibernateStatus) {
	v.value = val
	v.isSet = true
}

func (v NullableHibernateStatus) IsSet() bool {
	return v.isSet
}

func (v *NullableHibernateStatus) Unset() {
	v.value = nil
	v.isSet = false
}

func NewNullableHibernateStatus(val *HibernateStatus) *NullableHibernateStatus {
	return &NullableHibernateStatus{value: val, isSet: true}
}

func (v NullableHibernateStatus) MarshalJSON() ([]byte, error) {
	return json.Marshal(v.value)
}

func (v *NullableHibernateStatus) UnmarshalJSON(src []byte) error {
	v.isSet = true
	return json.Unmarshal(src, &v.value)
}
