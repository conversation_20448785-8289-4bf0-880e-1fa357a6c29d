/*
Devtron Labs

No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)

API version: 1.0.0
*/

// Code generated by OpenAPI Generator (https://openapi-generator.tech); DO NOT EDIT.

package openapi

import (
	"encoding/json"
)

// DesiredManifestRequest struct for DesiredManifestRequest
type DesiredManifestRequest struct {
	// helm app id
	AppId *string `json:"appId,omitempty"`
	Resource *ResourceIdentifier `json:"resource,omitempty"`
}

// NewDesiredManifestRequest instantiates a new DesiredManifestRequest object
// This constructor will assign default values to properties that have it defined,
// and makes sure properties required by API are set, but the set of arguments
// will change when the set of required properties is changed
func NewDesiredManifestRequest() *DesiredManifestRequest {
	this := DesiredManifestRequest{}
	return &this
}

// NewDesiredManifestRequestWithDefaults instantiates a new DesiredManifestRequest object
// This constructor will only assign default values to properties that have it defined,
// but it doesn't guarantee that properties required by API are set
func NewDesiredManifestRequestWithDefaults() *DesiredManifestRequest {
	this := DesiredManifestRequest{}
	return &this
}

// GetAppId returns the AppId field value if set, zero value otherwise.
func (o *DesiredManifestRequest) GetAppId() string {
	if o == nil || o.AppId == nil {
		var ret string
		return ret
	}
	return *o.AppId
}

// GetAppIdOk returns a tuple with the AppId field value if set, nil otherwise
// and a boolean to check if the value has been set.
func (o *DesiredManifestRequest) GetAppIdOk() (*string, bool) {
	if o == nil || o.AppId == nil {
		return nil, false
	}
	return o.AppId, true
}

// HasAppId returns a boolean if a field has been set.
func (o *DesiredManifestRequest) HasAppId() bool {
	if o != nil && o.AppId != nil {
		return true
	}

	return false
}

// SetAppId gets a reference to the given string and assigns it to the AppId field.
func (o *DesiredManifestRequest) SetAppId(v string) {
	o.AppId = &v
}

// GetResource returns the Resource field value if set, zero value otherwise.
func (o *DesiredManifestRequest) GetResource() ResourceIdentifier {
	if o == nil || o.Resource == nil {
		var ret ResourceIdentifier
		return ret
	}
	return *o.Resource
}

// GetResourceOk returns a tuple with the Resource field value if set, nil otherwise
// and a boolean to check if the value has been set.
func (o *DesiredManifestRequest) GetResourceOk() (*ResourceIdentifier, bool) {
	if o == nil || o.Resource == nil {
		return nil, false
	}
	return o.Resource, true
}

// HasResource returns a boolean if a field has been set.
func (o *DesiredManifestRequest) HasResource() bool {
	if o != nil && o.Resource != nil {
		return true
	}

	return false
}

// SetResource gets a reference to the given ResourceIdentifier and assigns it to the Resource field.
func (o *DesiredManifestRequest) SetResource(v ResourceIdentifier) {
	o.Resource = &v
}

func (o DesiredManifestRequest) MarshalJSON() ([]byte, error) {
	toSerialize := map[string]interface{}{}
	if o.AppId != nil {
		toSerialize["appId"] = o.AppId
	}
	if o.Resource != nil {
		toSerialize["resource"] = o.Resource
	}
	return json.Marshal(toSerialize)
}

type NullableDesiredManifestRequest struct {
	value *DesiredManifestRequest
	isSet bool
}

func (v NullableDesiredManifestRequest) Get() *DesiredManifestRequest {
	return v.value
}

func (v *NullableDesiredManifestRequest) Set(val *DesiredManifestRequest) {
	v.value = val
	v.isSet = true
}

func (v NullableDesiredManifestRequest) IsSet() bool {
	return v.isSet
}

func (v *NullableDesiredManifestRequest) Unset() {
	v.value = nil
	v.isSet = false
}

func NewNullableDesiredManifestRequest(val *DesiredManifestRequest) *NullableDesiredManifestRequest {
	return &NullableDesiredManifestRequest{value: val, isSet: true}
}

func (v NullableDesiredManifestRequest) MarshalJSON() ([]byte, error) {
	return json.Marshal(v.value)
}

func (v *NullableDesiredManifestRequest) UnmarshalJSON(src []byte) error {
	v.isSet = true
	return json.Unmarshal(src, &v.value)
}


