{{- if $.Values.ingress.enabled -}}
---
apiVersion: extensions/v1beta1
kind: Ingress
metadata:
  name: {{ template ".Chart.Name .fullname" . }}-ingress
  namespace: {{ $.Values.NameSpace }}
  labels:
    app: {{ template ".Chart.Name .name" . }}
    appId: {{ $.Values.app | quote }}
    envId: {{ $.Values.env | quote }}
    chart: {{ template ".Chart.Name .chart" . }}
    release: {{ .Release.Name }}
    {{- if .Values.ingress.labels }}
{{ toYaml .Values.ingress.labels | indent 4 }}
    {{- end }}
{{- if .Values.ingress.annotations }}
  annotations:
{{ toYaml .Values.ingress.annotations | indent 4 }}
{{- end }}
{{/*  annotations:
    nginx.ingress.kubernetes.io/rewrite-target: /
    nginx.ingress.kubernetes.io/ssl-redirect: "false" */}}
spec:
  rules:
  - host: {{ .Values.ingress.host }}
    http:
      paths:
        - path: {{ .Values.ingress.path }}
          backend:
            serviceName: {{ template ".servicename" . }}
            servicePort: {{ (index .Values.ContainerPort 0).servicePort }}
{{- end }}
  {{- if $.Values.ingressInternal.enabled }}
---
apiVersion: extensions/v1beta1
kind: Ingress
metadata:
  name: {{ template ".Chart.Name .fullname" . }}-ingress-internal
  namespace: {{ $.Values.NameSpace }}
  labels:
    app: {{ template ".Chart.Name .name" . }}
    appId: {{ $.Values.app | quote }}
    envId: {{ $.Values.env | quote }}
    chart: {{ template ".Chart.Name .chart" . }}
    release: {{ .Release.Name }}
{{- if .Values.ingressInternal.annotations }}
  annotations:
{{ toYaml .Values.ingressInternal.annotations | indent 4 }}
{{- end }}
  {{/*  annotations:
nginx.ingress.kubernetes.io/rewrite-target: /
nginx.ingress.kubernetes.io/ssl-redirect: "false" */}}
spec:
  rules:
    - host: {{ .Values.ingressInternal.host }}
      http:
        paths:
          - path: {{ .Values.ingressInternal.path }}
            backend:
              serviceName: {{ template ".servicename" . }}
              servicePort: {{ (index .Values.ContainerPort 0).servicePort }}
  {{- end }}
