{{ if eq .Values.kind "Job" }}
apiVersion: batch/v1
kind: Job
metadata:
  name: {{ include ".Chart.Name .fullname" $ }}-{{ $.Values.releaseVersion }}
  labels:
    app: {{ template ".Chart.Name .name" $ }}
    chart: {{ template ".Chart.Name .chart" $ }}
    release: {{ $.Release.Name }}
    releaseVersion: {{ $.Values.releaseVersion | quote }}
    pipelineName: {{ .Values.pipelineName }}
spec:
  {{- include "job-template-spec" . | indent 2 }}
{{ end }}
