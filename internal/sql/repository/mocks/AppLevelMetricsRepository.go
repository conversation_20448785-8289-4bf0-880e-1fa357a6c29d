// Code generated by mockery v2.14.0. DO NOT EDIT.

package mocks

import (
	"github.com/devtron-labs/devtron/pkg/deployment/manifest/deployedAppMetrics/repository"
	mock "github.com/stretchr/testify/mock"
)

// AppLevelMetricsRepository is an autogenerated mock type for the AppLevelMetricsRepository type
type AppLevelMetricsRepository struct {
	mock.Mock
}

// FindByAppId provides a mock function with given fields: id
func (_m *AppLevelMetricsRepository) FindByAppId(id int) (*repository.AppLevelMetrics, error) {
	ret := _m.Called(id)

	var r0 *repository.AppLevelMetrics
	if rf, ok := ret.Get(0).(func(int) *repository.AppLevelMetrics); ok {
		r0 = rf(id)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*repository.AppLevelMetrics)
		}
	}

	var r1 error
	if rf, ok := ret.Get(1).(func(int) error); ok {
		r1 = rf(id)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// Save provides a mock function with given fields: metrics
func (_m *AppLevelMetricsRepository) Save(metrics *repository.AppLevelMetrics) error {
	ret := _m.Called(metrics)

	var r0 error
	if rf, ok := ret.Get(0).(func(*repository.AppLevelMetrics) error); ok {
		r0 = rf(metrics)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// Update provides a mock function with given fields: metrics
func (_m *AppLevelMetricsRepository) Update(metrics *repository.AppLevelMetrics) error {
	ret := _m.Called(metrics)

	var r0 error
	if rf, ok := ret.Get(0).(func(*repository.AppLevelMetrics) error); ok {
		r0 = rf(metrics)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

type mockConstructorTestingTNewAppLevelMetricsRepository interface {
	mock.TestingT
	Cleanup(func())
}

// NewAppLevelMetricsRepository creates a new instance of AppLevelMetricsRepository. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
func NewAppLevelMetricsRepository(t mockConstructorTestingTNewAppLevelMetricsRepository) *AppLevelMetricsRepository {
	mock := &AppLevelMetricsRepository{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}
