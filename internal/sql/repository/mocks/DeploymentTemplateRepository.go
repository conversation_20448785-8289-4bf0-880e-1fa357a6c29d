// Code generated by mockery v2.31.4. DO NOT EDIT.

package mocks

import (
	repository "github.com/devtron-labs/devtron/internal/sql/repository"
	mock "github.com/stretchr/testify/mock"
)

// DeploymentTemplateRepository is an autogenerated mock type for the DeploymentTemplateRepository type
type DeploymentTemplateRepository struct {
	mock.Mock
}

// FetchDeploymentHistoryWithChartRefs provides a mock function with given fields: appId, envId
func (_m *DeploymentTemplateRepository) FetchDeploymentHistoryWithChartRefs(appId int, envId int) ([]*repository.DeploymentTemplateComparisonMetadata, error) {
	ret := _m.Called(appId, envId)

	var r0 []*repository.DeploymentTemplateComparisonMetadata
	var r1 error
	if rf, ok := ret.Get(0).(func(int, int) ([]*repository.DeploymentTemplateComparisonMetadata, error)); ok {
		return rf(appId, envId)
	}
	if rf, ok := ret.Get(0).(func(int, int) []*repository.DeploymentTemplateComparisonMetadata); ok {
		r0 = rf(appId, envId)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]*repository.DeploymentTemplateComparisonMetadata)
		}
	}

	if rf, ok := ret.Get(1).(func(int, int) error); ok {
		r1 = rf(appId, envId)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// FetchLatestDeploymentWithChartRefs provides a mock function with given fields: appId, envId
func (_m *DeploymentTemplateRepository) FetchLatestDeploymentWithChartRefs(appId int, envId int) ([]*repository.DeploymentTemplateComparisonMetadata, error) {
	ret := _m.Called(appId, envId)

	var r0 []*repository.DeploymentTemplateComparisonMetadata
	var r1 error
	if rf, ok := ret.Get(0).(func(int, int) ([]*repository.DeploymentTemplateComparisonMetadata, error)); ok {
		return rf(appId, envId)
	}
	if rf, ok := ret.Get(0).(func(int, int) []*repository.DeploymentTemplateComparisonMetadata); ok {
		r0 = rf(appId, envId)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]*repository.DeploymentTemplateComparisonMetadata)
		}
	}

	if rf, ok := ret.Get(1).(func(int, int) error); ok {
		r1 = rf(appId, envId)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// FetchPipelineOverrideValues provides a mock function with given fields: id
func (_m *DeploymentTemplateRepository) FetchPipelineOverrideValues(id int) (string, error) {
	ret := _m.Called(id)

	var r0 string
	var r1 error
	if rf, ok := ret.Get(0).(func(int) (string, error)); ok {
		return rf(id)
	}
	if rf, ok := ret.Get(0).(func(int) string); ok {
		r0 = rf(id)
	} else {
		r0 = ret.Get(0).(string)
	}

	if rf, ok := ret.Get(1).(func(int) error); ok {
		r1 = rf(id)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// NewDeploymentTemplateRepository creates a new instance of DeploymentTemplateRepository. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewDeploymentTemplateRepository(t interface {
	mock.TestingT
	Cleanup(func())
}) *DeploymentTemplateRepository {
	mock := &DeploymentTemplateRepository{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}
