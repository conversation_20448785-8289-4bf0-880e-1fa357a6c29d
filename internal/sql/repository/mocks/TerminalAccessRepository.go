// Code generated by mockery v2.14.0. DO NOT EDIT.

package mocks

import (
	models "github.com/devtron-labs/devtron/internal/sql/models"
	mock "github.com/stretchr/testify/mock"
)

// TerminalAccessRepository is an autogenerated mock type for the TerminalAccessRepository type
type TerminalAccessRepository struct {
	mock.Mock
}

// FetchAllTemplates provides a mock function with given fields:
func (_m *TerminalAccessRepository) FetchAllTemplates() ([]*models.TerminalAccessTemplates, error) {
	ret := _m.Called()

	var r0 []*models.TerminalAccessTemplates
	if rf, ok := ret.Get(0).(func() []*models.TerminalAccessTemplates); ok {
		r0 = rf()
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]*models.TerminalAccessTemplates)
		}
	}

	var r1 error
	if rf, ok := ret.Get(1).(func() error); ok {
		r1 = rf()
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// FetchTerminalAccessTemplate provides a mock function with given fields: templateName
func (_m *TerminalAccessRepository) FetchTerminalAccessTemplate(templateName string) (*models.TerminalAccessTemplates, error) {
	ret := _m.Called(templateName)

	var r0 *models.TerminalAccessTemplates
	if rf, ok := ret.Get(0).(func(string) *models.TerminalAccessTemplates); ok {
		r0 = rf(templateName)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*models.TerminalAccessTemplates)
		}
	}

	var r1 error
	if rf, ok := ret.Get(1).(func(string) error); ok {
		r1 = rf(templateName)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// GetAllRunningUserTerminalData provides a mock function with given fields:
func (_m *TerminalAccessRepository) GetAllRunningUserTerminalData() ([]*models.UserTerminalAccessData, error) {
	ret := _m.Called()

	var r0 []*models.UserTerminalAccessData
	if rf, ok := ret.Get(0).(func() []*models.UserTerminalAccessData); ok {
		r0 = rf()
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]*models.UserTerminalAccessData)
		}
	}

	var r1 error
	if rf, ok := ret.Get(1).(func() error); ok {
		r1 = rf()
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// GetUserTerminalAccessData provides a mock function with given fields: id
func (_m *TerminalAccessRepository) GetUserTerminalAccessData(id int) (*models.UserTerminalAccessData, error) {
	ret := _m.Called(id)

	var r0 *models.UserTerminalAccessData
	if rf, ok := ret.Get(0).(func(int) *models.UserTerminalAccessData); ok {
		r0 = rf(id)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*models.UserTerminalAccessData)
		}
	}

	var r1 error
	if rf, ok := ret.Get(1).(func(int) error); ok {
		r1 = rf(id)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// GetUserTerminalAccessDataByUser provides a mock function with given fields: userId
func (_m *TerminalAccessRepository) GetUserTerminalAccessDataByUser(userId int32) ([]*models.UserTerminalAccessData, error) {
	ret := _m.Called(userId)

	var r0 []*models.UserTerminalAccessData
	if rf, ok := ret.Get(0).(func(int32) []*models.UserTerminalAccessData); ok {
		r0 = rf(userId)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]*models.UserTerminalAccessData)
		}
	}

	var r1 error
	if rf, ok := ret.Get(1).(func(int32) error); ok {
		r1 = rf(userId)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// SaveUserTerminalAccessData provides a mock function with given fields: data
func (_m *TerminalAccessRepository) SaveUserTerminalAccessData(data *models.UserTerminalAccessData) error {
	ret := _m.Called(data)

	var r0 error
	if rf, ok := ret.Get(0).(func(*models.UserTerminalAccessData) error); ok {
		r0 = rf(data)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// UpdateUserTerminalAccessData provides a mock function with given fields: data
func (_m *TerminalAccessRepository) UpdateUserTerminalAccessData(data *models.UserTerminalAccessData) error {
	ret := _m.Called(data)

	var r0 error
	if rf, ok := ret.Get(0).(func(*models.UserTerminalAccessData) error); ok {
		r0 = rf(data)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// UpdateUserTerminalStatus provides a mock function with given fields: id, status
func (_m *TerminalAccessRepository) UpdateUserTerminalStatus(id int, status string) error {
	ret := _m.Called(id, status)

	var r0 error
	if rf, ok := ret.Get(0).(func(int, string) error); ok {
		r0 = rf(id, status)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

type mockConstructorTestingTNewTerminalAccessRepository interface {
	mock.TestingT
	Cleanup(func())
}

// NewTerminalAccessRepository creates a new instance of TerminalAccessRepository. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
func NewTerminalAccessRepository(t mockConstructorTestingTNewTerminalAccessRepository) *TerminalAccessRepository {
	mock := &TerminalAccessRepository{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}
