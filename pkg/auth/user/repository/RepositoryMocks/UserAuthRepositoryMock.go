// Code generated by mockery v2.14.0. DO NOT EDIT.

package repomock

import (
	repository2 "github.com/devtron-labs/devtron/pkg/auth/user/repository"
	pg "github.com/go-pg/pg"
	mock "github.com/stretchr/testify/mock"
)

// UserAuthRepository is an autogenerated mock type for the UserAuthRepository type
type UserAuthRepository struct {
	mock.Mock
}

// CreateDefaultHelmPolicies provides a mock function with given fields: team, entityName, env, tx
func (_m *UserAuthRepository) CreateDefaultHelmPolicies(team string, entityName string, env string, tx *pg.Tx) (bool, error) {
	ret := _m.Called(team, entityName, env, tx)

	var r0 bool
	if rf, ok := ret.Get(0).(func(string, string, string, *pg.Tx) bool); ok {
		r0 = rf(team, entityName, env, tx)
	} else {
		r0 = ret.Get(0).(bool)
	}

	var r1 error
	if rf, ok := ret.Get(1).(func(string, string, string, *pg.Tx) error); ok {
		r1 = rf(team, entityName, env, tx)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// CreateDefaultPolicies provides a mock function with given fields: team, entityName, env, tx
func (_m *UserAuthRepository) CreateDefaultPolicies(team string, entityName string, env string, tx *pg.Tx) (bool, error) {
	ret := _m.Called(team, entityName, env, tx)

	var r0 bool
	if rf, ok := ret.Get(0).(func(string, string, string, *pg.Tx) bool); ok {
		r0 = rf(team, entityName, env, tx)
	} else {
		r0 = ret.Get(0).(bool)
	}

	var r1 error
	if rf, ok := ret.Get(1).(func(string, string, string, *pg.Tx) error); ok {
		r1 = rf(team, entityName, env, tx)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// CreateDefaultPoliciesForGlobalEntity provides a mock function with given fields: entity, entityName, action, tx
func (_m *UserAuthRepository) CreateDefaultPoliciesForGlobalEntity(entity string, entityName string, action string, tx *pg.Tx) (bool, error) {
	ret := _m.Called(entity, entityName, action, tx)

	var r0 bool
	if rf, ok := ret.Get(0).(func(string, string, string, *pg.Tx) bool); ok {
		r0 = rf(entity, entityName, action, tx)
	} else {
		r0 = ret.Get(0).(bool)
	}

	var r1 error
	if rf, ok := ret.Get(1).(func(string, string, string, *pg.Tx) error); ok {
		r1 = rf(entity, entityName, action, tx)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// CreateRole provides a mock function with given fields: userModel, tx
func (_m *UserAuthRepository) CreateRole(userModel *repository2.RoleModel, tx *pg.Tx) (*repository2.RoleModel, error) {
	ret := _m.Called(userModel, tx)

	var r0 *repository2.RoleModel
	if rf, ok := ret.Get(0).(func(*repository2.RoleModel, *pg.Tx) *repository2.RoleModel); ok {
		r0 = rf(userModel, tx)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*repository2.RoleModel)
		}
	}

	var r1 error
	if rf, ok := ret.Get(1).(func(*repository2.RoleModel, *pg.Tx) error); ok {
		r1 = rf(userModel, tx)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// CreateRoleForSuperAdminIfNotExists provides a mock function with given fields: tx
func (_m *UserAuthRepository) CreateRoleForSuperAdminIfNotExists(tx *pg.Tx) (bool, error) {
	ret := _m.Called(tx)

	var r0 bool
	if rf, ok := ret.Get(0).(func(*pg.Tx) bool); ok {
		r0 = rf(tx)
	} else {
		r0 = ret.Get(0).(bool)
	}

	var r1 error
	if rf, ok := ret.Get(1).(func(*pg.Tx) error); ok {
		r1 = rf(tx)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// CreateUserRoleMapping provides a mock function with given fields: userRoleModel, tx
func (_m *UserAuthRepository) CreateUserRoleMapping(userRoleModel *repository2.UserRoleModel, tx *pg.Tx) (*repository2.UserRoleModel, error) {
	ret := _m.Called(userRoleModel, tx)

	var r0 *repository2.UserRoleModel
	if rf, ok := ret.Get(0).(func(*repository2.UserRoleModel, *pg.Tx) *repository2.UserRoleModel); ok {
		r0 = rf(userRoleModel, tx)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*repository2.UserRoleModel)
		}
	}

	var r1 error
	if rf, ok := ret.Get(1).(func(*repository2.UserRoleModel, *pg.Tx) error); ok {
		r1 = rf(userRoleModel, tx)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// DeleteRole provides a mock function with given fields: role, tx
func (_m *UserAuthRepository) DeleteRole(role *repository2.RoleModel, tx *pg.Tx) error {
	ret := _m.Called(role, tx)

	var r0 error
	if rf, ok := ret.Get(0).(func(*repository2.RoleModel, *pg.Tx) error); ok {
		r0 = rf(role, tx)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// DeleteUserRoleByRoleId provides a mock function with given fields: roleId, tx
func (_m *UserAuthRepository) DeleteUserRoleByRoleId(roleId int, tx *pg.Tx) error {
	ret := _m.Called(roleId, tx)

	var r0 error
	if rf, ok := ret.Get(0).(func(int, *pg.Tx) error); ok {
		r0 = rf(roleId, tx)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// DeleteUserRoleMapping provides a mock function with given fields: userRoleModel, tx
func (_m *UserAuthRepository) DeleteUserRoleMapping(userRoleModel *repository2.UserRoleModel, tx *pg.Tx) (bool, error) {
	ret := _m.Called(userRoleModel, tx)

	var r0 bool
	if rf, ok := ret.Get(0).(func(*repository2.UserRoleModel, *pg.Tx) bool); ok {
		r0 = rf(userRoleModel, tx)
	} else {
		r0 = ret.Get(0).(bool)
	}

	var r1 error
	if rf, ok := ret.Get(1).(func(*repository2.UserRoleModel, *pg.Tx) error); ok {
		r1 = rf(userRoleModel, tx)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// GetAllRole provides a mock function with given fields:
func (_m *UserAuthRepository) GetAllRole() ([]repository2.RoleModel, error) {
	ret := _m.Called()

	var r0 []repository2.RoleModel
	if rf, ok := ret.Get(0).(func() []repository2.RoleModel); ok {
		r0 = rf()
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]repository2.RoleModel)
		}
	}

	var r1 error
	if rf, ok := ret.Get(1).(func() error); ok {
		r1 = rf()
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// GetRoleByFilter provides a mock function with given fields: entity, team, app, env, act, accessType
func (_m *UserAuthRepository) GetRoleByFilter(entity string, team string, app string, env string, act string, accessType string) (repository2.RoleModel, error) {
	ret := _m.Called(entity, team, app, env, act, accessType)

	var r0 repository2.RoleModel
	if rf, ok := ret.Get(0).(func(string, string, string, string, string, string) repository2.RoleModel); ok {
		r0 = rf(entity, team, app, env, act, accessType)
	} else {
		r0 = ret.Get(0).(repository2.RoleModel)
	}

	var r1 error
	if rf, ok := ret.Get(1).(func(string, string, string, string, string, string) error); ok {
		r1 = rf(entity, team, app, env, act, accessType)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// GetRoleById provides a mock function with given fields: id
func (_m *UserAuthRepository) GetRoleById(id int) (*repository2.RoleModel, error) {
	ret := _m.Called(id)

	var r0 *repository2.RoleModel
	if rf, ok := ret.Get(0).(func(int) *repository2.RoleModel); ok {
		r0 = rf(id)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*repository2.RoleModel)
		}
	}

	var r1 error
	if rf, ok := ret.Get(1).(func(int) error); ok {
		r1 = rf(id)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// GetRoleByRoles provides a mock function with given fields: roles
func (_m *UserAuthRepository) GetRoleByRoles(roles []string) ([]repository2.RoleModel, error) {
	ret := _m.Called(roles)

	var r0 []repository2.RoleModel
	if rf, ok := ret.Get(0).(func([]string) []repository2.RoleModel); ok {
		r0 = rf(roles)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]repository2.RoleModel)
		}
	}

	var r1 error
	if rf, ok := ret.Get(1).(func([]string) error); ok {
		r1 = rf(roles)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// GetRolesByActionAndAccessType provides a mock function with given fields: action, accessType
func (_m *UserAuthRepository) GetRolesByActionAndAccessType(action string, accessType string) ([]repository2.RoleModel, error) {
	ret := _m.Called(action, accessType)

	var r0 []repository2.RoleModel
	if rf, ok := ret.Get(0).(func(string, string) []repository2.RoleModel); ok {
		r0 = rf(action, accessType)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]repository2.RoleModel)
		}
	}

	var r1 error
	if rf, ok := ret.Get(1).(func(string, string) error); ok {
		r1 = rf(action, accessType)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// GetRolesByGroupId provides a mock function with given fields: userId
func (_m *UserAuthRepository) GetRolesByGroupId(userId int32) ([]*repository2.RoleModel, error) {
	ret := _m.Called(userId)

	var r0 []*repository2.RoleModel
	if rf, ok := ret.Get(0).(func(int32) []*repository2.RoleModel); ok {
		r0 = rf(userId)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]*repository2.RoleModel)
		}
	}

	var r1 error
	if rf, ok := ret.Get(1).(func(int32) error); ok {
		r1 = rf(userId)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// GetRolesByUserId provides a mock function with given fields: userId
func (_m *UserAuthRepository) GetRolesByUserId(userId int32) ([]repository2.RoleModel, error) {
	ret := _m.Called(userId)

	var r0 []repository2.RoleModel
	if rf, ok := ret.Get(0).(func(int32) []repository2.RoleModel); ok {
		r0 = rf(userId)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]repository2.RoleModel)
		}
	}

	var r1 error
	if rf, ok := ret.Get(1).(func(int32) error); ok {
		r1 = rf(userId)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// GetRolesForApp provides a mock function with given fields: appName
func (_m *UserAuthRepository) GetRolesForApp(appName string) ([]*repository2.RoleModel, error) {
	ret := _m.Called(appName)

	var r0 []*repository2.RoleModel
	if rf, ok := ret.Get(0).(func(string) []*repository2.RoleModel); ok {
		r0 = rf(appName)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]*repository2.RoleModel)
		}
	}

	var r1 error
	if rf, ok := ret.Get(1).(func(string) error); ok {
		r1 = rf(appName)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// GetRolesForChartGroup provides a mock function with given fields: chartGroupName
func (_m *UserAuthRepository) GetRolesForChartGroup(chartGroupName string) ([]*repository2.RoleModel, error) {
	ret := _m.Called(chartGroupName)

	var r0 []*repository2.RoleModel
	if rf, ok := ret.Get(0).(func(string) []*repository2.RoleModel); ok {
		r0 = rf(chartGroupName)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]*repository2.RoleModel)
		}
	}

	var r1 error
	if rf, ok := ret.Get(1).(func(string) error); ok {
		r1 = rf(chartGroupName)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// GetRolesForEnvironment provides a mock function with given fields: envName, envIdentifier
func (_m *UserAuthRepository) GetRolesForEnvironment(envName string, envIdentifier string) ([]*repository2.RoleModel, error) {
	ret := _m.Called(envName, envIdentifier)

	var r0 []*repository2.RoleModel
	if rf, ok := ret.Get(0).(func(string, string) []*repository2.RoleModel); ok {
		r0 = rf(envName, envIdentifier)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]*repository2.RoleModel)
		}
	}

	var r1 error
	if rf, ok := ret.Get(1).(func(string, string) error); ok {
		r1 = rf(envName, envIdentifier)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// GetRolesForProject provides a mock function with given fields: teamName
func (_m *UserAuthRepository) GetRolesForProject(teamName string) ([]*repository2.RoleModel, error) {
	ret := _m.Called(teamName)

	var r0 []*repository2.RoleModel
	if rf, ok := ret.Get(0).(func(string) []*repository2.RoleModel); ok {
		r0 = rf(teamName)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]*repository2.RoleModel)
		}
	}

	var r1 error
	if rf, ok := ret.Get(1).(func(string) error); ok {
		r1 = rf(teamName)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// GetUserRoleMappingByUserId provides a mock function with given fields: userId
func (_m *UserAuthRepository) GetUserRoleMappingByUserId(userId int32) ([]*repository2.UserRoleModel, error) {
	ret := _m.Called(userId)

	var r0 []*repository2.UserRoleModel
	if rf, ok := ret.Get(0).(func(int32) []*repository2.UserRoleModel); ok {
		r0 = rf(userId)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]*repository2.UserRoleModel)
		}
	}

	var r1 error
	if rf, ok := ret.Get(1).(func(int32) error); ok {
		r1 = rf(userId)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// SyncOrchestratorToCasbin provides a mock function with given fields: team, entityName, env, tx
func (_m *UserAuthRepository) SyncOrchestratorToCasbin(team string, entityName string, env string, tx *pg.Tx) (bool, error) {
	ret := _m.Called(team, entityName, env, tx)

	var r0 bool
	if rf, ok := ret.Get(0).(func(string, string, string, *pg.Tx) bool); ok {
		r0 = rf(team, entityName, env, tx)
	} else {
		r0 = ret.Get(0).(bool)
	}

	var r1 error
	if rf, ok := ret.Get(1).(func(string, string, string, *pg.Tx) error); ok {
		r1 = rf(team, entityName, env, tx)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// UpdateTriggerPolicyForTerminalAccess provides a mock function with given fields:
func (_m *UserAuthRepository) UpdateTriggerPolicyForTerminalAccess() error {
	ret := _m.Called()

	var r0 error
	if rf, ok := ret.Get(0).(func() error); ok {
		r0 = rf()
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

type mockConstructorTestingTNewUserAuthRepository interface {
	mock.TestingT
	Cleanup(func())
}

// NewUserAuthRepository creates a new instance of UserAuthRepository. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
func NewUserAuthRepository(t mockConstructorTestingTNewUserAuthRepository) *UserAuthRepository {
	mock := &UserAuthRepository{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}
