/*
 * Copyright (c) 2024. Devtron Inc.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package helper

import "github.com/devtron-labs/devtron/pkg/resourceQualifiers"

func QualifierComparator(a, b resourceQualifiers.Qualifier) bool {
	return GetPriority(a) < GetPriority(b)
}
func FindMinWithComparator(variableScope []*resourceQualifiers.QualifierMapping, comparator func(a, b resourceQualifiers.Qualifier) bool) *resourceQualifiers.QualifierMapping {
	if len(variableScope) == 0 {
		return nil
	}
	min := variableScope[0]
	for _, val := range variableScope {
		if comparator(resourceQualifiers.Qualifier(val.QualifierId), resourceQualifiers.Qualifier(min.QualifierId)) {
			min = val
		}
	}
	return min
}

func GetPriority(qualifier resourceQualifiers.Qualifier) int {
	switch qualifier {
	case resourceQualifiers.GLOBAL_QUALIFIER:
		return 5
	default:
		return 0
	}
}
