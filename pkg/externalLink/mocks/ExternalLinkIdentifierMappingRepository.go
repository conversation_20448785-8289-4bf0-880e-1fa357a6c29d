// Code generated by mockery v2.14.1. DO NOT EDIT.

package mocks

import (
	externalLink "github.com/devtron-labs/devtron/pkg/externalLink"
	mock "github.com/stretchr/testify/mock"

	pg "github.com/go-pg/pg"
)

// ExternalLinkIdentifierMappingRepository is an autogenerated mock type for the ExternalLinkIdentifierMappingRepository type
type ExternalLinkIdentifierMappingRepository struct {
	mock.Mock
}

// FindAllActive provides a mock function with given fields:
func (_m *ExternalLinkIdentifierMappingRepository) FindAllActive() ([]externalLink.ExternalLinkIdentifierMapping, error) {
	ret := _m.Called()

	var r0 []externalLink.ExternalLinkIdentifierMapping
	if rf, ok := ret.Get(0).(func() []externalLink.ExternalLinkIdentifierMapping); ok {
		r0 = rf()
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]externalLink.ExternalLinkIdentifierMapping)
		}
	}

	var r1 error
	if rf, ok := ret.Get(1).(func() error); ok {
		r1 = rf()
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// FindAllActiveByClusterId provides a mock function with given fields: clusterId
func (_m *ExternalLinkIdentifierMappingRepository) FindAllActiveByClusterId(clusterId int) ([]externalLink.ExternalLinkIdentifierMapping, error) {
	ret := _m.Called(clusterId)

	var r0 []externalLink.ExternalLinkIdentifierMapping
	if rf, ok := ret.Get(0).(func(int) []externalLink.ExternalLinkIdentifierMapping); ok {
		r0 = rf(clusterId)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]externalLink.ExternalLinkIdentifierMapping)
		}
	}

	var r1 error
	if rf, ok := ret.Get(1).(func(int) error); ok {
		r1 = rf(clusterId)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// FindAllActiveByExternalLinkId provides a mock function with given fields: linkId
func (_m *ExternalLinkIdentifierMappingRepository) FindAllActiveByExternalLinkId(linkId int) ([]*externalLink.ExternalLinkIdentifierMapping, error) {
	ret := _m.Called(linkId)

	var r0 []*externalLink.ExternalLinkIdentifierMapping
	if rf, ok := ret.Get(0).(func(int) []*externalLink.ExternalLinkIdentifierMapping); ok {
		r0 = rf(linkId)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]*externalLink.ExternalLinkIdentifierMapping)
		}
	}

	var r1 error
	if rf, ok := ret.Get(1).(func(int) error); ok {
		r1 = rf(linkId)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// FindAllActiveByLinkIdentifier provides a mock function with given fields: identifier, clusterId
func (_m *ExternalLinkIdentifierMappingRepository) FindAllActiveByLinkIdentifier(identifier *externalLink.LinkIdentifier, clusterId int) ([]externalLink.ExternalLinkIdentifierMappingData, error) {
	ret := _m.Called(identifier, clusterId)

	var r0 []externalLink.ExternalLinkIdentifierMappingData
	if rf, ok := ret.Get(0).(func(*externalLink.LinkIdentifier, int) []externalLink.ExternalLinkIdentifierMappingData); ok {
		r0 = rf(identifier, clusterId)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]externalLink.ExternalLinkIdentifierMappingData)
		}
	}

	var r1 error
	if rf, ok := ret.Get(1).(func(*externalLink.LinkIdentifier, int) error); ok {
		r1 = rf(identifier, clusterId)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// FindAllActiveLinkIdentifierData provides a mock function with given fields:
func (_m *ExternalLinkIdentifierMappingRepository) FindAllActiveLinkIdentifierData() ([]externalLink.ExternalLinkIdentifierMappingData, error) {
	ret := _m.Called()

	var r0 []externalLink.ExternalLinkIdentifierMappingData
	if rf, ok := ret.Get(0).(func() []externalLink.ExternalLinkIdentifierMappingData); ok {
		r0 = rf()
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]externalLink.ExternalLinkIdentifierMappingData)
		}
	}

	var r1 error
	if rf, ok := ret.Get(1).(func() error); ok {
		r1 = rf()
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// FindAllByExternalLinkId provides a mock function with given fields: linkId
func (_m *ExternalLinkIdentifierMappingRepository) FindAllByExternalLinkId(linkId int) ([]*externalLink.ExternalLinkIdentifierMapping, error) {
	ret := _m.Called(linkId)

	var r0 []*externalLink.ExternalLinkIdentifierMapping
	if rf, ok := ret.Get(0).(func(int) []*externalLink.ExternalLinkIdentifierMapping); ok {
		r0 = rf(linkId)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]*externalLink.ExternalLinkIdentifierMapping)
		}
	}

	var r1 error
	if rf, ok := ret.Get(1).(func(int) error); ok {
		r1 = rf(linkId)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// Save provides a mock function with given fields: externalLinksClusters, tx
func (_m *ExternalLinkIdentifierMappingRepository) Save(externalLinksClusters *externalLink.ExternalLinkIdentifierMapping, tx *pg.Tx) error {
	ret := _m.Called(externalLinksClusters, tx)

	var r0 error
	if rf, ok := ret.Get(0).(func(*externalLink.ExternalLinkIdentifierMapping, *pg.Tx) error); ok {
		r0 = rf(externalLinksClusters, tx)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// Update provides a mock function with given fields: link, tx
func (_m *ExternalLinkIdentifierMappingRepository) Update(link *externalLink.ExternalLinkIdentifierMapping, tx *pg.Tx) error {
	ret := _m.Called(link, tx)

	var r0 error
	if rf, ok := ret.Get(0).(func(*externalLink.ExternalLinkIdentifierMapping, *pg.Tx) error); ok {
		r0 = rf(link, tx)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// UpdateAllActiveToInActive provides a mock function with given fields: Id, tx
func (_m *ExternalLinkIdentifierMappingRepository) UpdateAllActiveToInActive(Id int, tx *pg.Tx) error {
	ret := _m.Called(Id, tx)

	var r0 error
	if rf, ok := ret.Get(0).(func(int, *pg.Tx) error); ok {
		r0 = rf(Id, tx)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

type mockConstructorTestingTNewExternalLinkIdentifierMappingRepository interface {
	mock.TestingT
	Cleanup(func())
}

// NewExternalLinkIdentifierMappingRepository creates a new instance of ExternalLinkIdentifierMappingRepository. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
func NewExternalLinkIdentifierMappingRepository(t mockConstructorTestingTNewExternalLinkIdentifierMappingRepository) *ExternalLinkIdentifierMappingRepository {
	mock := &ExternalLinkIdentifierMappingRepository{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}
