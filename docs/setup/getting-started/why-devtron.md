
# Why Devtron?
 

Kubernetes is an open-source container orchestration tool that enables administrators to seamlessly deploy, manage and scale containerized apps in a wide variety of production environments. It works on a wide variety of platforms and cloud deployment models. By organizing apps into a cluster of containers that run on virtualized host operating systems, Kubernetes enables businesses to manage IT workloads efficiently.

In the process, however, Kubernetes has also introduced a lot of new complexities for developers and systems administrators alike.

To improve the usage of Kubernetes, several tools are needed to be integrated. But using these tools at the same time, however, is cumbersome and complex. These tools do not communicate with one another to manage different aspects of the application lifecycle, such as CI/CD, security, cost, observability, and stabilization.

This is where **Devtron** comes into the picture! 

<p align="center"><img src="../assets/readme-comic.png"></p>
 
The need to declaratively manage Kubernetes clusters and application delivery is what is driving **Devtron** on Kubernetes.
 
Devtron is an open-source modular product that provides a `seamless` and `implementation-agnostic uniform interface`, that can be integrated with both open-source and commercial tools across the entire application lifecycle.

With Devtron, you can efficiently handle security, stability, cost, and more in a unified experience.
 


 
 

