## v0.6.24

## Bugs
- fix: nats clients panic in ea-mode (#4162)
- fix: query number changed (#4163)
- fix: duplication of tags on giving same key-value pair (#4139)
- fix: error handling in image scan (#4150)
- fix: valuesOverrideResponse (#4149)
## Enhancements
- feat: custom tag (#3847)
- feat: enchancements in scoped variables (#4069)
## Documentation
- docs: Reverted to relative path with extension type (#4156)
- doc: Changed Relative Path to Absolute Path (#4155)
- docs: Mentioned Partial Cloning Capability of Devtron (#4142)
- docs: Added Exclude Git Files section in Existing Doc (#4136)
## Others
- chore: moved methods use by workflowdagexecutor to it (#4147)
- chore: fix sql 181 script  (#4144)


