## v0.6.12

## Bugs
- fix: chart repository fixes (#2730)
- fix: conditionally setting s3 config in workflow object (#3014)
- fix: fixed condition for updating health timeline and status (#3003)
- fix: added deployment app type support for app create api, separated cron time for helm app status update (#2995)
- fix: helm deploy hibernate fix (#2983)
- fix: duplicate name in charts and chart Group (#2942)
- fix: sorting app environment containers in app listing api  (#2935)
- fix: Multigit bug fix in case of preCD and postCD (#2920)
- fix: Injected support for cloning linked ci pipelines via workflow cloning API (#2944)
- fix: k8s log stream cpu issue (#2929)
- fix: cd pipeline delete, 404 issue (#2939)
- fix: finished on time update fix (#2932)
- fix: updated log type for pipeline status methods (#2926)
- fix: pvc mounted on pods for cache handling  (#2912)
- fix: Installed apps rbac optimisation (#2918)
- fix: event notification calls missing for Build Success and Cd Deployment Success for helm type (#2898)
- fix: ingress url missing (#2915)
## Enhancements
- feat: deployment pipeline partially delete. (#2950)
- feat: jira issue validator plugin added (#2968)
- feat: add configuration for only logging pg queries exceeding a threshold duration (#2946)
- feat: add api to change deployment app type for all cd pipelines in an environment (#2975)
- feat: App grouping BE (#2979)
- feat: Resource browser child ref (#2913)
- feat: cluster bearer token hide from dashboard (#2894)
- feat: added option to propagate custom tag to k8s resources for that application (#2841)
- feat: argo stack upgrade (#2597)
- feat: Showing app status on app listing page (#2799)
## Documentation
- docs: sso login doc (#2854)
- docs: Some correction in Documentation at install devtron/ configuration / ingress setup (#2981)
- docs: sso login doc (#2854)
- docs: Added preset plugins (#2904)
- docs: mount pvc (#2941)
- docs: minor updates ingress (#2931)
## Others
- bug: resolve failing bulk pipeline delete api (#3042)
- removed server mode hardcoding (#3031)
- fi: fixed incorrect userId in ci audit entries (#3011)
- plugin jira icon added (#2977)
- task: added api for getting default template values (#2905)


